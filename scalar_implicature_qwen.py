#!/usr/bin/env python3
"""
实验2: 标量蕴含测试 - 使用Qwen3-4B模型
读取scalar_implicature_test.csv文件，对每一行的陈述句和问句进行推理
输出模型选择("是"或"不是")以及相应的对数概率
"""

import pandas as pd
import torch
import numpy as np
from transformers import AutoModelForCausalLM, AutoTokenizer
from datetime import datetime
import os
import sys
from tqdm import tqdm

# 获取设备数量
num_devices = torch.cuda.device_count()
print("GPU 设备数量: ", num_devices)
# 打印所有设备名称和信息
for i in range(num_devices):
    print(torch.cuda.get_device_properties(i))

# 定义logsoftmax用于从scores获取logprobs
logsoftmax = torch.nn.LogSoftmax(dim=-1)

def getLogProbContinuation(
        initialSequence,
        continuation,
        model,
        tokenizer,
        model_name='',
        preface=''):
    """
    从语言模型获取不同回答类型的对数概率的辅助函数。
    支持多种模型类型：Qwen、Llama、ChatGLM等
    """

    initialSequence = preface + initialSequence
    prompt = preface + initialSequence + continuation

    # 检测是否为聊天模型（支持多种模型类型）
    is_chat_model = any(keyword in model_name.lower() for keyword in
                       ["chat", "instruct", "it", "sft", "dpo", "rlhf"])

    # 特殊处理ChatGLM模型
    if "chatglm" in model_name.lower():
        # ChatGLM使用特殊的对话格式
        formatted_prompt = f"[Round 1]\n\n问：{initialSequence.strip()}\n\n答：{continuation}"
        input_ids = tokenizer.encode(formatted_prompt, return_tensors="pt").to("cuda:0" if torch.cuda.is_available() else "cpu")

        # 获取prompt部分的长度
        prompt_only = f"[Round 1]\n\n问：{initialSequence.strip()}\n\n答："
        input_ids_prompt = tokenizer.encode(prompt_only, return_tensors="pt")

    elif is_chat_model and hasattr(tokenizer, 'apply_chat_template'):
        # 构造聊天消息格式
        messages = [
            {"role": "user", "content": initialSequence.strip()},
            {"role": "assistant", "content": continuation}
        ]

        # 使用聊天模板
        input_ids = tokenizer.apply_chat_template(
            messages,
            return_tensors="pt",
            add_generation_prompt=False
        ).to("cuda:0" if torch.cuda.is_available() else "cpu")

        # 为了知道continuation的形状，单独tokenize
        messages_noCont = [
            {"role": "user", "content": initialSequence.strip()},
            {"role": "assistant", "content": ""}
        ]
        input_ids_prompt = tokenizer.apply_chat_template(
            messages_noCont,
            return_tensors="pt",
            add_generation_prompt=True
        )
    else:
        # 对于非聊天模型，直接tokenize
        input_ids_prompt = tokenizer(
            initialSequence.strip(),
            return_tensors="pt",
        ).input_ids
        input_ids = tokenizer(
            prompt,
            return_tensors="pt",
        ).input_ids.to("cuda:0" if torch.cuda.is_available() else "cpu")

    # 通过模型
    with torch.no_grad():
        outputs = model(input_ids)

    # 将logits转换为概率
    # 移除我们不感兴趣的EOS logit
    qwen_output_scores = logsoftmax(outputs.logits[0][:-1])

    # 在token ids处检索log probs
    # 将input_ids转换为形状为[n_tokens, 1]的张量
    # 切掉sos token，以获得在前面上下文条件下的实际token预测
    input_ids_probs = input_ids[:, 1:].squeeze().unsqueeze(-1)

    # 在正确的token位置检索
    conditionalLogProbs = torch.gather(
        qwen_output_scores,
        dim=-1,
        index=input_ids_probs
    ).flatten()

    # 切片输出，只获得continuation的分数，不包括上下文
    continuationConditionalLogProbs = conditionalLogProbs[
        (input_ids_prompt.shape[-1]-1):
    ]

    # 计算continuation log prob
    sentLogProb = torch.sum(continuationConditionalLogProbs).item()
    meanLogProb = torch.mean(continuationConditionalLogProbs).item()

    return sentLogProb, meanLogProb

class ScalarImplicatureTest:
    def __init__(self, model_path, model_name="auto", prompt_version="improved"):
        """
        初始化标量蕴含测试类

        Args:
            model_path: 模型的本地路径
            model_name: 模型名称，用于识别模型类型和生成文件名
            prompt_version: 提示词版本 ("original", "improved", "detailed", "conversational")
        """
        self.model_path = model_path
        self.model_name = model_name if model_name != "auto" else self._detect_model_name(model_path)
        self.prompt_version = prompt_version
        self.model = None
        self.tokenizer = None
        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"

    def _detect_model_name(self, model_path):
        """从模型路径自动检测模型名称"""
        path_lower = model_path.lower()

        # 常见模型名称检测
        if "qwen" in path_lower:
            if "3-4b" in path_lower or "3_4b" in path_lower:
                return "Qwen3-4B"
            elif "2.5" in path_lower:
                return "Qwen2.5"
            elif "2-" in path_lower or "2_" in path_lower:
                return "Qwen2"
            else:
                return "Qwen"
        elif "llama" in path_lower:
            if "3.1" in path_lower:
                return "Llama3.1"
            elif "3" in path_lower:
                return "Llama3"
            else:
                return "Llama"
        elif "chatglm" in path_lower:
            return "ChatGLM"
        elif "baichuan" in path_lower:
            return "Baichuan"
        elif "internlm" in path_lower:
            return "InternLM"
        else:
            # 从路径中提取最后一个文件夹名作为模型名
            return os.path.basename(model_path.rstrip('/'))
        
    def load_model(self):
        """加载模型和tokenizer"""
        print(f"正在从 {self.model_path} 加载 {self.model_name} 模型...")

        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型路径不存在: {self.model_path}")

        try:
            # 特殊处理ChatGLM模型
            if "chatglm" in self.model_name.lower():
                print("检测到ChatGLM模型，使用原生加载方式...")
                try:
                    # 直接导入ChatGLM的类
                    import sys
                    sys.path.append(self.model_path)

                    from modeling_chatglm import ChatGLMForConditionalGeneration
                    from tokenization_chatglm import ChatGLMTokenizer

                    print("正在加载ChatGLM tokenizer...")
                    self.tokenizer = ChatGLMTokenizer.from_pretrained(self.model_path)
                    print("ChatGLM Tokenizer加载成功!")

                    print("正在加载ChatGLM模型...")
                    self.model = ChatGLMForConditionalGeneration.from_pretrained(
                        self.model_path,
                        torch_dtype=torch.float16
                    ).to(self.device)
                    print("ChatGLM模型加载成功!")
                    return

                except Exception as chatglm_e:
                    print(f"ChatGLM原生加载失败: {chatglm_e}")
                    print("尝试使用AutoModel方式...")

                    # 尝试使用AutoModel
                    try:
                        self.tokenizer = AutoTokenizer.from_pretrained(
                            self.model_path,
                            trust_remote_code=True,
                            use_fast=False
                        )
                        print("ChatGLM Tokenizer (Auto模式) 加载成功!")

                        # 使用AutoModel而不是AutoModelForCausalLM
                        from transformers import AutoModel
                        self.model = AutoModel.from_pretrained(
                            self.model_path,
                            torch_dtype=torch.float16,
                            trust_remote_code=True
                        ).to(self.device)
                        print("ChatGLM模型 (Auto模式) 加载成功!")
                        return
                    except Exception as chatglm_e2:
                        print(f"ChatGLM Auto模式也失败: {chatglm_e2}")
                        print("尝试标准加载方式...")

            # 标准加载方式
            print("正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                local_files_only=True
            )
            print("Tokenizer加载成功!")

            # 加载模型
            print("正在加载模型...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map='auto',
                torch_dtype=torch.float16,
                trust_remote_code=True,
                local_files_only=True
            )
            print("模型加载成功!")

        except ImportError as e:
            if "sentencepiece" in str(e):
                print(f"错误: 缺少sentencepiece依赖")
                print("请运行: pip install sentencepiece")
                print("或者: conda install -c conda-forge sentencepiece")
                print("或者尝试使用其他模型（如Qwen系列）")
                raise
            else:
                print(f"导入错误: {e}")
                raise
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("尝试使用不同的加载参数...")

            # 尝试不同的加载方式
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(
                    self.model_path,
                    trust_remote_code=True
                )
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float16,
                    trust_remote_code=True
                ).to(self.device)
                print("使用备用方式加载成功!")
            except Exception as e2:
                print(f"备用加载方式也失败: {e2}")
                raise
        self.model.eval()
        
        print(f"模型加载完成! 设备: {self.device}")
        print(f"模型数据类型: {self.model.dtype}")
        
    def get_model_predictions(self, statement, question, prompt_version="improved"):
        """
        获取模型对给定陈述句和问句的预测结果

        Args:
            statement: 陈述句
            question: 问句
            prompt_version: 提示词版本 ("original", "improved", "detailed", "conversational")

        Returns:
            dict: 包含模型选择和对数概率的结果
        """

        # 多种提示词版本
        prompts = {
            "original": f"""基于以下陈述句回答问题：

陈述句：{statement}

问题：{question}

请仅回答"是"或"不是"：""",

            "improved": f"""你是一个语言推理专家。请仔细分析以下陈述句，然后基于语言的隐含意义回答问题。

在日常语言中，当我们使用某个程度词时，通常暗示我们不认为更强程度的词适用。例如，说"它还行"通常暗示"它不是很好"。

陈述句：{statement}

问题：{question}

请仔细思考陈述句中词语的隐含意义，然后回答"是"或"不是"：""",

            "detailed": f"""作为语言学专家，请分析以下语言推理任务：

背景知识：
- 标量蕴含是指当使用较弱的表达时，通常暗示不适用更强的表达
- 例如："有些学生通过了考试"通常暗示"不是所有学生都通过了"
- 程度词存在强弱关系：好 < 优秀 < 完美；大 < 巨大；喜欢 < 爱

任务：根据陈述句的隐含意义判断问题的答案。

陈述句：{statement}
问题：{question}

分析步骤：
1. 识别陈述句中的关键词
2. 考虑是否存在更强程度的表达
3. 判断说话者是否暗示不适用更强表达

请回答"是"或"不是"：""",

            "conversational": f"""请想象你在和朋友聊天，朋友告诉你："{statement}"

现在有人问你：{question}

根据你朋友话语的真实含义（不只是字面意思），你会回答"是"还是"不是"？

请回答："""
        }

        prompt = prompts.get(prompt_version, prompts["improved"])
        
        # 两个可能的答案
        answer_yes = "是"
        answer_no = "不是"
        
        # 获取"是"的对数概率
        log_prob_yes, mean_log_prob_yes = getLogProbContinuation(
            prompt,
            answer_yes,
            self.model,
            self.tokenizer,
            model_name=self.model_path
        )
        
        # 获取"不是"的对数概率
        log_prob_no, mean_log_prob_no = getLogProbContinuation(
            prompt,
            answer_no,
            self.model,
            self.tokenizer,
            model_name=self.model_path
        )
        
        # 基于对数概率确定模型选择
        if mean_log_prob_yes > mean_log_prob_no:
            chosen_response = "是"
        else:
            chosen_response = "不是"
        
        return {
            "模型的选择": chosen_response,
            "Mean_logprob_answer_是": mean_log_prob_yes,
            "Mean_logprob_answer_不是": mean_log_prob_no,
            "Sentence_logprob_answer_是": log_prob_yes,
            "Sentence_logprob_answer_不是": log_prob_no
        }
    
    def process_csv(self, csv_path="scalar_implicature_test.csv"):
        """
        处理scalar_implicature_test.csv文件
        
        Args:
            csv_path: CSV文件路径
            
        Returns:
            pd.DataFrame: 包含预测结果的DataFrame
        """
        
        # 读取CSV文件
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
        
        df = pd.read_csv(csv_path)
        print(f"读取CSV文件: {csv_path}")
        print(f"共 {len(df)} 行数据")
        
        # 确保模型已加载
        if self.model is None or self.tokenizer is None:
            self.load_model()
        
        # 存储结果
        results = []
        
        # 遍历每一行数据
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="处理中"):
            try:
                statement = row['陈述句']
                question = row['问句']
                
                print(f"\n处理第 {idx+1} 行:")
                print(f"陈述句: {statement}")
                print(f"问句: {question}")
                
                # 获取模型预测
                prediction = self.get_model_predictions(statement, question, self.prompt_version)
                
                print(f"模型选择: {prediction['模型的选择']}")
                print(f"'是'的对数概率: {prediction['Mean_logprob_answer_是']:.6f}")
                print(f"'不是'的对数概率: {prediction['Mean_logprob_answer_不是']:.6f}")
                
                # 合并原始数据和预测结果
                result_row = {**row.to_dict(), **prediction}
                results.append(result_row)
                
            except Exception as e:
                print(f"处理第 {idx+1} 行时出错: {e}")
                # 添加错误行，保持数据完整性
                error_result = {
                    **row.to_dict(),
                    "模型的选择": "ERROR",
                    "Mean_logprob_answer_是": np.nan,
                    "Mean_logprob_answer_不是": np.nan,
                    "Sentence_logprob_answer_是": np.nan,
                    "Sentence_logprob_answer_不是": np.nan,
                    "error": str(e)
                }
                results.append(error_result)
        
        return pd.DataFrame(results)
    
    def save_results(self, results_df, output_path=None):
        """
        保存结果到CSV文件
        
        Args:
            results_df: 结果DataFrame
            output_path: 输出文件路径（可选）
        """
        
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            output_path = f"scalar_implicature_results_{self.model_name}_{timestamp}.csv"
        
        results_df.to_csv(output_path, index=False, encoding='utf-8')
        print(f"\n结果已保存到: {output_path}")
        
        # 打印结果摘要
        print(f"\n=== 结果摘要 ===")
        print(f"总计处理: {len(results_df)} 行")
        
        if "模型的选择" in results_df.columns:
            choice_counts = results_df["模型的选择"].value_counts()
            print(f"模型选择分布:")
            for choice, count in choice_counts.items():
                print(f"  {choice}: {count} 次 ({count/len(results_df)*100:.1f}%)")

def get_model_selection():
    """获取用户的模型选择"""
    print("=" * 60)
    print("    实验2: 标量蕴含测试")
    print("=" * 60)

    print("\n请输入要测试的模型信息:")

    # 直接让用户输入模型路径
    model_path = input("模型路径: ").strip()

    # 让用户输入模型名称（可选）
    model_name = input("模型名称 (用于文件命名，留空自动检测): ").strip()

    if not model_name:
        model_name = "auto"

    return model_path, model_name

def get_prompt_selection():
    """获取用户的提示词选择"""
    print("\n可选的提示词版本:")
    print("1. original - 原始简单版本")
    print("2. improved - 改进版本（默认）")
    print("3. detailed - 详细说明版本")
    print("4. conversational - 对话式版本")

    choice = input("\n请选择提示词版本 (1-4，默认2): ").strip()

    prompt_versions = {
        "1": "original",
        "2": "improved",
        "3": "detailed",
        "4": "conversational"
    }

    return prompt_versions.get(choice, "improved")

def main():
    """主函数"""

    csv_path = "scalar_implicature_test.csv"

    try:
        # 获取模型选择
        model_path, model_name = get_model_selection()

        # 获取提示词选择
        prompt_version = get_prompt_selection()

        print(f"\n使用模型: {model_name}")
        print(f"模型路径: {model_path}")
        print(f"提示词版本: {prompt_version}")
        print("-" * 60)

        # 创建测试实例
        test = ScalarImplicatureTest(
            model_path=model_path,
            model_name=model_name,
            prompt_version=prompt_version
        )

        # 处理CSV文件
        results = test.process_csv(csv_path)

        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
        output_path = f"scalar_implicature_results_{model_name}_{prompt_version}_{timestamp}.csv"

        # 保存结果
        test.save_results(results, output_path)

        print(f"\n实验2完成!")
        print(f"使用模型: {model_name}")
        print(f"提示词版本: {prompt_version}")
        print(f"结果文件: {output_path}")

    except Exception as e:
        print(f"运行时出错: {e}")
        raise

if __name__ == "__main__":
    main()